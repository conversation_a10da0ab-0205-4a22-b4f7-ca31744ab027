import asyncio
import logging
from io import By<PERSON><PERSON>
from typing import Annotated, cast
from urllib.parse import unquote

from fastapi import APIRouter, Body, Path, UploadFile
from prime_jobs import JobScheduler, scheduler_dep
from prime_service_kit.fastapi_utils.pagination import PaginationResponse, pagination_args_type
from prime_service_kit.shared_types import AccountArgumentType
from prime_shared.common_types import AccountIdType
from starlette.responses import Response

from service.db import DesignDocsTable, service_dal_depends
from service.dependencies import redis_type
from service.errors import DesignDocAttachContextError, DesignDocDuplicateError, DesignDocUpdateNotAllowed
from service.logic import (
    DesignDocUploadMetadata,
    _get_design_docs_source,
    create_design_doc_job,
    get_existing_files,
    get_extend_external_design_docs,
    get_external_design_docs,
    merge_pdfs,
    merge_urls,
    save_design_doc_by_url,
    upload_design_doc,
)
from service.models import (
    AttachContextToDesignDocReq,
    ContextType,
    DesignDocByMultipleUrlsReq,
    DesignDocBySingleUrlReq,
    DesignDocFileUploadResponse,
    DesignDocResponse,
    ExtendDesignDocResponse,
)
from service.models.design_docs import DesignDocType
from service.services_clients import ServicesClients

LOGGER = logging.getLogger("design-docs")

design_docs_api = APIRouter(prefix="/design-docs", tags=["design-docs"])


@design_docs_api.get(
    "/{account_id}",
    description="Get all design docs",
    name="get-design-docs",
)
async def get_design_docs(
    account_id: AccountArgumentType,
    pagination_args: pagination_args_type,
    service_dal: service_dal_depends,
    redis_client: redis_type,
    doc_source_type: DesignDocType | None = None,
) -> PaginationResponse[DesignDocResponse]:
    LOGGER.info("Getting all design docs keys for account %s", account_id)
    docs_response = await get_external_design_docs(
        account_id=account_id,
        pagination_args=pagination_args,
        service_dal=service_dal,
        doc_source_type=doc_source_type,
        redis_client=redis_client,
    )
    return docs_response


@design_docs_api.delete("/{account_id}/delete/{doc_id}")
async def delete_design_doc(account_id: AccountArgumentType, doc_id: int, service_dal: service_dal_depends) -> None:
    await service_dal.design_docs_dal.delete_design_doc_by_id(account_id, doc_id)


@design_docs_api.get("/{account_id}/download/{doc_id}")
async def download_design_doc(account_id: AccountArgumentType, doc_id: int, service_dal: service_dal_depends) -> bytes:
    design_doc = await service_dal.design_docs_dal.get_design_docs_by_id(account_id, doc_id)
    design_docs_source = await _get_design_docs_source(account_id)
    file_content = await ServicesClients.files_api().download_origin_file_for_source(
        account_id, design_docs_source.id, cast(str, design_doc.file_origin_id)
    )
    return cast(bytes, Response(content=file_content, media_type="application/octet-stream"))


@design_docs_api.get("/{account_id}/{doc_id}")
async def get_design_doc_by_id(
    account_id: AccountArgumentType,
    doc_id: int,
    service_dal: service_dal_depends,
    redis_client: redis_type,
) -> ExtendDesignDocResponse:
    return await get_extend_external_design_docs(
        account_id=account_id, doc_id=doc_id, service_dal=service_dal, redis_client=redis_client
    )


@design_docs_api.post("/{account_id}/{doc_id}/reprocess")
async def reprocess_design_doc(
    account_id: AccountArgumentType,
    created_by: Annotated[str, Body(embed=True)],
    doc_id: int,
    service_dal: service_dal_depends,
    scheduler: scheduler_dep,
    redis_client: redis_type,
) -> None:
    doc = await service_dal.design_docs_dal.get_design_docs_by_id(account_id, doc_id)
    await create_design_doc_job([doc], account_id, created_by, scheduler, redis_client)


@design_docs_api.post("/{account_id}/{doc_id}/attach-context")
async def attach_context_to_design_doc(
    account_id: AccountArgumentType,
    doc_id: int,
    attach_ctx_req: AttachContextToDesignDocReq,
    service_dal: service_dal_depends,
    scheduler: scheduler_dep,
    redis_client: redis_type,
) -> None:
    if attach_ctx_req.context_type == ContextType.AGENT:
        try:
            conversation = await ServicesClients.agent_api().get_conversation(
                account_id=account_id, conversation_id=attach_ctx_req.context_id
            )
            await service_dal.design_docs_dal.update_design_doc_fields(
                doc_id, account_id, agent_conversation_id=conversation.conversation_id
            )
        except Exception as e:
            LOGGER.exception("Error attaching context to design doc %s", doc_id)
            raise DesignDocAttachContextError(doc_id, attach_ctx_req.context_id) from e
    else:
        raise ValueError(f"Invalid context type: {attach_ctx_req.context_type}")

    await reprocess_design_doc(account_id, attach_ctx_req.created_by, doc_id, service_dal, scheduler, redis_client)


async def _design_docs_file_upload(
    account_id: AccountIdType,
    created_by: str,
    service_dal: service_dal_depends,
    design_doc_file: UploadFile,
) -> DesignDocsTable:
    original_filename = unquote(cast(str, design_doc_file.filename))
    LOGGER.info("Creating new design doc %s by file", original_filename)
    file_content = await design_doc_file.read()
    origin_id = await upload_design_doc(account_id, file_content, cast(str, design_doc_file.filename))
    return await service_dal.design_docs_dal.add_design_doc(
        account_id, original_filename, created_by, origin_id=origin_id, doc_source_type=DesignDocType.ORIGINAL
    )


@design_docs_api.post("/{account_id}/file")
async def design_docs_by_file(
    account_id: AccountArgumentType,
    created_by: str,
    design_doc_files: list[UploadFile],
    service_dal: service_dal_depends,
    scheduler: scheduler_dep,
    redis_client: redis_type,
    process_as_one: bool = False,
) -> DesignDocFileUploadResponse:
    filenames = [doc_file.filename for doc_file in design_doc_files if doc_file.filename]
    LOGGER.info("Creating new %s design docs by files for account %s", filenames, account_id)
    if process_as_one and len(design_doc_files) > 1:
        LOGGER.info("Combining %s design doc files into one", len(design_doc_files))
        combined_content = merge_pdfs([await f.read() for f in design_doc_files])
        combined_filename = f"{design_doc_files[0].filename} (+{len(design_doc_files) - 1})"
        design_doc_files = [UploadFile(file=BytesIO(combined_content), filename=combined_filename)]

    if existing_files := await get_existing_files(account_id, filenames):
        raise DesignDocDuplicateError([unquote(f.origin_id) for f in existing_files])
    docs = [
        await _design_docs_file_upload(account_id, created_by, service_dal, design_doc)
        for design_doc in design_doc_files
    ]
    await create_design_doc_job(docs, account_id, created_by, scheduler, redis_client)
    return DesignDocFileUploadResponse(
        files_uploaded=len(docs),
        file_names=[doc.title for doc in docs],
    )


@design_docs_api.put("/{account_id}/file/{doc_id}")
async def update_design_doc_by_file(
    account_id: AccountArgumentType,
    doc_id: int,
    design_doc_file: UploadFile,
    service_dal: service_dal_depends,
    scheduler: scheduler_dep,
    redis_client: redis_type,
) -> DesignDocFileUploadResponse:
    LOGGER.info("Updating design doc %s for account %s with new file", doc_id, account_id)

    existing_doc = await service_dal.design_docs_dal.get_design_docs_by_id(account_id, doc_id)
    if existing_doc.url:
        raise DesignDocUpdateNotAllowed(doc_id, f"file update not allowed for url-based design doc: {existing_doc.url}")

    file_content = await design_doc_file.read()
    await upload_design_doc(account_id, file_content, cast(str, existing_doc.file_origin_id), allow_update=True)
    await service_dal.design_docs_dal.update_design_doc_fields(
        doc_id,
        account_id,
        # Clear previous analysis results since we have a new file
        summary=None,
        top_recommendations=None,
        policy_recommendations=None,
        mermaid_diagram=None,
        attack_scenarios=None,
        attack_scenario_dataflow_diagram=None,
    )
    await create_design_doc_job([existing_doc], account_id, existing_doc.created_by, scheduler, redis_client)
    return DesignDocFileUploadResponse(
        files_uploaded=1,
        file_names=[existing_doc.title],
    )


@design_docs_api.post("/{account_id}/gdrive")
async def design_docs_by_gdrive(
    account_id: AccountArgumentType,
    url_request: DesignDocBySingleUrlReq,
    service_dal: service_dal_depends,
    scheduler: scheduler_dep,
    redis_client: redis_type,
) -> DesignDocFileUploadResponse:
    LOGGER.info("Creating gdrive design docs for account %s with url %s", account_id, url_request.url)
    return await _create_design_doc_from_urls(
        account_id, [url_request.url], service_dal, scheduler, redis_client, url_request.created_by, False
    )


@design_docs_api.post("/{account_id}/confluence")
async def design_docs_by_confluence(
    account_id: AccountArgumentType,
    url_request: DesignDocBySingleUrlReq,
    service_dal: service_dal_depends,
    scheduler: scheduler_dep,
    redis_client: redis_type,
) -> DesignDocFileUploadResponse:
    LOGGER.info("Creating confluence design docs for account %s with url %s", account_id, url_request.url)
    return await _create_design_doc_from_urls(
        account_id, [url_request.url], service_dal, scheduler, redis_client, url_request.created_by, False
    )


@design_docs_api.post("/{account_id}/reference")
async def create_design_docs_by_reference(
    account_id: AccountArgumentType,
    creator: Annotated[str, Body(embed=True)],
    url: Annotated[str, Body(embed=True)],
    case_id: Annotated[int, Body(embed=True)],
    service_dal: service_dal_depends,
) -> None:
    LOGGER.info("Creating design docs by reference %s", account_id)
    design_doc_metadata: DesignDocUploadMetadata = await save_design_doc_by_url(account_id, url)
    await service_dal.design_docs_dal.add_design_doc(
        account_id=account_id,
        title=design_doc_metadata.name,
        created_by=str(creator),
        origin_id=design_doc_metadata.origin_id,
        url=url,
        doc_source_type=DesignDocType.REFERENCE,
        case_id=case_id,
    )


@design_docs_api.post("/{account_id}/urls")
async def design_docs_by_urls(
    account_id: AccountArgumentType,
    urls_request: DesignDocByMultipleUrlsReq,
    service_dal: service_dal_depends,
    scheduler: scheduler_dep,
    redis_client: redis_type,
) -> DesignDocFileUploadResponse:
    LOGGER.info("Creating design docs for account %s with urls %s", account_id, urls_request)
    return await _create_design_doc_from_urls(
        account_id,
        urls_request.urls,
        service_dal,
        scheduler,
        redis_client,
        urls_request.created_by,
        urls_request.process_as_one,
    )


async def _create_design_doc_from_urls(
    account_id: AccountIdType,
    urls: list[str],
    service_dal: service_dal_depends,
    scheduler: JobScheduler,
    redis_client: redis_type,
    created_by: str,
    process_as_one: bool,
) -> DesignDocFileUploadResponse:
    LOGGER.info("Creating design docs for account %s with urls %s, combined: %s", account_id, urls, process_as_one)
    new_docs = []
    upload_metadata: list[DesignDocUploadMetadata]
    if process_as_one:
        upload_metadata = [await merge_urls(account_id, urls)]
    else:
        save_tasks = [save_design_doc_by_url(account_id, url) for url in urls]
        upload_metadata = await asyncio.gather(*save_tasks)
    for metadata in upload_metadata:
        new_doc = await service_dal.design_docs_dal.add_design_doc(
            account_id,
            metadata.name,
            created_by,
            origin_id=metadata.origin_id,
            url=metadata.url,
            doc_source_type=DesignDocType.URL,
        )
        new_docs.append(new_doc)
    await create_design_doc_job(new_docs, account_id, created_by, scheduler, redis_client)
    return DesignDocFileUploadResponse(files_uploaded=len(new_docs), file_names=[doc.title for doc in new_docs])


@design_docs_api.post(
    "/{account_id}/case-id/{case_id}",
)
async def create_design_doc_container(  # noqa: PLR0913
    account_id: AccountArgumentType,
    case_id: Annotated[int, Path(description="Case ID")],
    created_by: Annotated[str, Body(embed=True)],
    service_dal: service_dal_depends,
    scheduler: scheduler_dep,
    redis_client: redis_type,
) -> DesignDocFileUploadResponse:
    case = await ServicesClients().cases_api().get_case_by_id(account_id, case_id)
    new_doc = await service_dal.design_docs_dal.add_design_doc(
        account_id,
        case.title,
        created_by,
        case_id=case_id,
        doc_source_type=DesignDocType.CONTAINER,
    )
    await create_design_doc_job([new_doc], account_id, created_by, scheduler, redis_client)
    return DesignDocFileUploadResponse(files_uploaded=1, file_names=[case.title])
